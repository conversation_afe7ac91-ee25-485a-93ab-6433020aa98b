import request from '@/common/utils/ajax'
import { nmbService, xmbtest } from '../../base'

const API_PATHS = {
  LIST: 'pasture/manager/pageV2', // 养殖场分页列表
  ADD: 'pasture/addV2', // 新增养殖场
  EDIT: 'pasture/editV2', // 修改养殖场
  DETAIL: 'pasture/manager/info', // 获取养殖场详情
  PENLIST: 'pasture/pen/list', // 获取圈舍列表
  LIVESTOCKPAGE: 'pastureLivestock/page', // 获取养殖场下活畜列表
  LIVESTOCKVARIETIESLIST:'livestock/livestockVarieties/list', // 活畜品种列表
}
export function pasturePage(param) {
  return request.ajax(xmbtest + API_PATHS.LIST, param, 'POST').then((res) => res.data)
}
export function pastureAdd(param) {
  return request.ajax(xmbtest + API_PATHS.ADD, param, 'POST').then((res) => res.data)
}

export function pastureEdit(param) {
  return request.ajax(xmbtest + API_PATHS.EDIT, param, 'POST').then((res) => res.data)
}

export function pastureDetail(param) {
  return request.ajax(xmbtest + API_PATHS.DETAIL, param, 'POST').then((res) => res.data)
}

export function penList(param) {
  return request.ajax(xmbtest + API_PATHS.PENLIST, param, 'POST').then((res) => res.data)
}

export function livestockPage(param) {
  return request.ajax(xmbtest + API_PATHS.LIVESTOCKPAGE, param, 'POST').then((res) => res.data)
}

export function livestockVarietiesList(param) {
  return request.ajax(xmbtest + API_PATHS.LIVESTOCKVARIETIESLIST, param, 'POST').then((res) => res.data)
}