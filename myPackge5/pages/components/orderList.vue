<template>
  <view class="page-container">
	<view class="u-p-b-20 box-l">
		 <view class="title flex-1"><text class="u-m-l-15"></text>订单列表</view>
		 <view class="w-100">
			 <u-icon name="close-circle"></u-icon>
		 </view>
	</view>
    <!-- 列表项循环 -->
	<view class="scroll" v-if="listData.length > 0">
		
		<view 
		  v-for="(item, index) in listData" 
		  :key="index" 
		  class="card-wrapper"
		  @click="choose(item)"
		>
		 <view class="ch">
			 <view
			   class="checkbox" 
			 >
			    <u-image width="40rpx"  height="40rpx" :src="`/static/img/${item.checked?'choose.png':'nochoose.png'}`"></u-image>
				
			 </view>
		 </view>
		 <view class="card">
			  <!-- 卡片头部 -->
			  <view class="card-header">
				<text class="header-id">{{ item.purchaseOrderCode }}</text>
				<text class="header-status">已验收{{item.checked}}</text>
			  </view>

			  <!-- 卡片内容 -->
			  <view class="card-content">
				
				
				<view class="info-item box-l">
					<view class="w-100 f-26 c-999">项目经理：</view>
					<view class="flex-1 f-26">{{ item.projectManagerName== null ? "--":item.projectManagerName }}</view>
				</view>
				<view class="info-item box-l">
					<view class="w-100 f-26 c-999">牛源地：</view>
					<view class="flex-1 f-26">{{ item.provinceName }}</view>
				</view>
				<view class="info-item box-l">
					<view class="w-100 f-26 c-999">牛经纪：</view>
					<view class="flex-1 f-26">{{ item.brokerName }}</view>
				</view>
			   <view class="info-item box-l">
			   	<view class="w-100 f-26 c-999">运输司机：</view>
			   	<view class="flex-1 f-26">{{ item.driverName }}</view>
			   </view>
				<view class="info-item box-l">
					<view class="w-100 f-26 c-999">采购数量：</view>
					<view class="flex-1 f-26">{{ item.livestockNum }}头</view>
				</view>
				<view class="info-item box-l">
					<view class="w-100 f-26 c-999">品种：</view>
					<view class="flex-1 f-26">{{ item.varietiesName }}/{{ item.categoryName }}</view>
				</view>
                <view class="info-item box-l">
                	<view class="w-100 f-26 c-999">订单日期：</view>
                	<view class="flex-1 f-26">{{ item.createTime}}</view>
                </view>
				
			  </view>
		  </view>
		</view>
		
    </view>
    <view v-else class="mt-300">
			
			<u-empty text="当前还没有已验收的订单数据" mode="list"></u-empty>
	</view>
    <!-- 确定按钮 -->
	<view class="btn-box">
      <view class="confirm-btn" @click="handleConfirm">确定</view>
	</view>
  </view>
</template>

<script>
const app = getApp();
export default {
  props: {
    // 列表数据
    listData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
	  obs:app.globalData.obs,
      /* listData: [
        {
          id: 'WR-NLB20250410001',
          status: '待商榷',
          manager: '回显（手机号）',
          source: '回显',
          economist: '回显（手机号）',
          transport: '司机（手机号、车牌号）',
          quantity: 10,
          date: '回显计划发车时间（年月日）',
          checked: false
        },
        {
          id: 'WR-NLB20250410001',
          status: '待商榷',
          manager: '回显（手机号）',
          source: '回显',
          economist: '回显（手机号）',
          transport: '司机（手机号、车牌号）',
          quantity: 10,
          date: '回显计划发车时间（年月日）',
          checked: false
        },
		{
		  id: 'WR-NLB20250410001',
		  status: '待商榷',
		  manager: '回显（手机号）',
		  source: '回显',
		  economist: '回显（手机号）',
		  transport: '司机（手机号、车牌号）',
		  quantity: 10,
		  date: '回显计划发车时间（年月日）',
		  checked: false
		},
		{
		  id: 'WR-NLB20250410001',
		  status: '待商榷',
		  manager: '回显（手机号）',
		  source: '回显',
		  economist: '回显（手机号）',
		  transport: '司机（手机号、车牌号）',
		  quantity: 10,
		  date: '回显计划发车时间（年月日）',
		  checked: false
		},
		{
		  id: 'WR-NLB20250410001',
		  status: '待商榷',
		  manager: '回显（手机号）',
		  source: '回显',
		  economist: '回显（手机号）',
		  transport: '司机（手机号、车牌号）',
		  quantity: 10,
		  date: '回显计划发车时间（年月日）',
		  checked: false
		},
		{
		  id: 'WR-NLB20250410001',
		  status: '待商榷',
		  manager: '回显（手机号）',
		  source: '回显',
		  economist: '回显（手机号）',
		  transport: '司机（手机号、车牌号）',
		  quantity: 10,
		  date: '回显计划发车时间（年月日）',
		  checked: false
		}
      ] */
    }
  },
  methods: {
    // 切换复选框状态
    toggleCheck(index) {
      this.listData[index].checked = !this.listData[index].checked
    },
    // 确定按钮事件
    handleConfirm() {
		
	  if(this.listData.length == 0){
		   this.$emit("ChooseOrder",null);
	  }
		
      const checkedItems = this.listData.find(item => item.checked)
     
	  
	  if(checkedItems == null) return uni.showToast({title:"请选择订单",icon:"none"});
	  
	  this.$emit("ChooseOrder",checkedItems);
	  
      // 这里可添加接口请求等业务逻辑
    },
	//选中元素
	choose(item){
		
		 //选中该元素后，其他元素则自动取消选择
		this.listData.forEach((i) => {
			i.checked = false;
		});
		item.checked = true;
		//强制更新组件数据
		this.$emit("UpdateListData",this.listData);
	}
  }
}
</script>


<style scoped lang="less">
.f-26{
	font-size: 26rpx;
}
.c-999{
	color: #999999;
}
.scroll{
	 margin-top: 50rpx;
	 padding-bottom: 140rpx;
}
.title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 24rpx;
	padding-bottom: 16rpx;
	height: 80rpx;
	line-height: 80rpx;
	position: fixed;
	top: 0rpx;
	left: 0rpx;
	width: 100%;
	background-color: #FFFFFF;
	z-index: 999;
	
}
.page-container {
  padding: 16rpx;
  background-color: #f5f5f5;
  height: calc(100% - 140rpx);  
}
.card{
	background-color: #FFFFFF;
	flex: 1;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	border-radius: 10rpx;
}
.ch{
	width: 80rpx;
}
/* 卡片容器 */
.card-wrapper {
  margin-bottom: 24rpx;
  border-radius: 12rpx;
  display: flex;
 
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 16rpx;
  background: linear-gradient( 260deg, #5ED26F 0%, #1CC271 100%);
  color: #FFFFFF;
  border-radius: 12rpx 12rpx 0 0;
}
.header-id {
  font-size: 26rpx;
  font-weight: bold;
  padding: 5rpx 0;
}
.header-status {
  font-size: 24rpx;
}

/* 卡片内容 */
.card-content {
  padding: 16rpx;
}
.info-item {
  margin-bottom: 12rpx;
  font-size: 24rpx;
  color: #333333;
}

/* 复选框样式 */
.checkbox {
  width: 40rpx;
  height: 40rpx;
  margin-top: 125rpx;
  margin-left: 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.checkbox.checked {
  background-color: #4CD964;
  border-color: #4CD964;
}
.check-mark {
  font-size: 28rpx;
  color: #FFFFFF;
}
.btn-box{
	width: 100%;
	height: 130rpx;
	position: fixed;
	bottom: 0;
	left: 0;
	background-color: #FFFFFF;
	padding: 20rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}
/* 确定按钮 */
.confirm-btn {
  flex: 1;
  margin-top: 10rpx;
  padding: 20rpx;
  text-align: center;
  background: linear-gradient( 101deg, #19AF77 0%, #40CA8F 100%);
  border-radius: 50px;
  color: #FFFFFF;
  font-size: 28rpx;
}
.w-100{
	width: 150rpx;
}
.box-l {
    display: flex !important;
	align-items: center;
	justify-content: space-between; 
}
.flex-1 {
    flex: 1;
}
</style>