# 库存统计查询功能完善说明

## 修改内容

### 1. 新增接口对接

#### 1.1 圈舍列表接口
- **接口**: `penList` from `@/api/pages/livestock/farm`
- **请求参数**:
  - `pageNum`: "1"
  - `pageSize`: "999"
  - `pastureId`: 养殖场ID
- **返回数据**: `penId`(圈舍ID), `penName`(圈舍名称)

#### 1.2 栏位列表接口
- **接口**: `fenceList` from `@/api/pages/livestock/farm`
- **请求参数**:
  - `pageNum`: 页数
  - `pageSize`: 每页数量
  - `pid`: 圈舍ID
- **返回数据**: `fenceCode`(栏位编号)

### 2. 查询条件完善

#### 2.1 新增查询字段
| 字段名 | 类型 | 说明 | 实现方式 |
|--------|------|------|----------|
| `varietiesId` | String | 品种ID | 下拉选择 |
| `penId` | String | 圈舍ID | 下拉选择 |
| `fenceCode` | String | 栏位编号 | 下拉选择 |
| `startWeight` | String | 开始重量 | 数字输入 |
| `endWeight` | String | 结束重量 | 数字输入 |
| `batch` | String | 批次 | 文本输入 |

#### 2.2 筛选表单更新
```javascript
form: {
    // 库存统计专用字段
    varietiesId: "", // 品种ID
    varietiesName: "", // 品种名称
    penId: "", // 圈舍ID
    penName: "", // 圈舍名称
    fenceCode: "", // 栏位编号
    startWeight: "", // 开始重量
    endWeight: "", // 结束重量
    batch: "", // 批次
}
```

### 3. 下拉选择器实现

#### 3.1 品种选择器
- 动态加载品种列表
- 显示品种名称，传递品种ID
- 支持选择后的回显

#### 3.2 圈舍选择器
- 根据养殖场ID动态加载圈舍列表
- 选择圈舍后自动加载对应栏位
- 切换圈舍时清空栏位选择

#### 3.3 栏位选择器
- 依赖圈舍选择，必须先选择圈舍
- 动态加载选中圈舍下的栏位列表
- 显示栏位编号

### 4. 级联选择逻辑

#### 4.1 养殖场 → 圈舍
```javascript
// 监听pastureId变化，自动加载圈舍列表
watch: {
    pastureId: {
        handler(newValue) {
            if (newValue) {
                this.loadPenList()
            } else {
                this.penOptions = []
                this.fenceOptions = []
            }
        },
        immediate: true
    }
}
```

#### 4.2 圈舍 → 栏位
```javascript
// 选择圈舍后加载栏位
confirmPen() {
    const selectedPen = this.penOptions[this.tempPenIndex];
    if (selectedPen) {
        // 清空栏位选择
        this.form.fenceCode = "";
        // 加载对应的栏位列表
        this.loadFenceList(selectedPen.penId);
    }
}
```

### 5. 用户体验优化

#### 5.1 选择提示
- 圈舍选择：未选择养殖场时提示"请先选择养殖场"
- 栏位选择：未选择圈舍时提示"请先选择圈舍"
- 栏位选择：圈舍无栏位时提示"该圈舍暂无栏位"

#### 5.2 数据联动
- 选择圈舍时自动清空栏位选择
- 重置表单时清空所有选择器状态
- 切换养殖场时重新加载圈舍列表

### 6. 参数传递

#### 6.1 组件props增强
```javascript
props: {
    pastureId: {
        type: String,
        default: ''
    }
}
```

#### 6.2 详情页面传参
```html
<filterPopup 
    :pastureId="pastureId" 
    @submitForm="submitForm" 
/>
```

### 7. 数据处理方法

#### 7.1 加载圈舍列表
```javascript
async loadPenList() {
    const res = await penList({
        pageNum: 1,
        pageSize: 999,
        pastureId: this.pastureId
    });
    this.penOptions = res.result.list;
}
```

#### 7.2 加载栏位列表
```javascript
async loadFenceList(penId) {
    const res = await fenceList({
        pageNum: 1,
        pageSize: 999,
        pid: penId
    });
    this.fenceOptions = res.result.list;
}
```

### 8. 筛选参数提交

#### 8.1 参数映射
```javascript
if (filterType === 'inventory') {
    if (form.varietiesId) filterData.varietiesId = form.varietiesId;
    if (form.penId) filterData.penId = form.penId;
    if (form.fenceCode) filterData.fenceCode = form.fenceCode;
    if (form.startWeight) filterData.startWeight = form.startWeight;
    if (form.endWeight) filterData.endWeight = form.endWeight;
    if (form.batch) filterData.batch = form.batch;
}
```

### 9. 功能特性

✅ **品种下拉选择**: 动态加载品种列表
✅ **圈舍下拉选择**: 根据养殖场ID加载圈舍
✅ **栏位下拉选择**: 根据圈舍ID加载栏位
✅ **级联选择**: 圈舍→栏位的级联关系
✅ **体重范围**: 开始重量和结束重量输入
✅ **批次筛选**: 批次文本输入
✅ **参数传递**: 正确的筛选参数提交
✅ **用户提示**: 友好的操作提示
✅ **数据重置**: 完整的表单重置功能

### 10. 测试建议

1. **基础功能测试**:
   - 测试品种选择器加载和选择
   - 测试圈舍选择器加载和选择
   - 测试栏位选择器加载和选择

2. **级联选择测试**:
   - 测试选择圈舍后栏位列表更新
   - 测试切换圈舍时栏位选择清空
   - 测试养殖场变化时圈舍列表更新

3. **边界情况测试**:
   - 测试未选择圈舍时点击栏位选择
   - 测试圈舍无栏位时的提示
   - 测试网络异常时的处理

4. **筛选功能测试**:
   - 测试各个筛选条件的提交
   - 测试筛选参数的正确传递
   - 测试重置功能的完整性

### 11. 注意事项

1. **数据依赖**: 栏位选择依赖圈舍选择，圈舍选择依赖养殖场ID
2. **参数传递**: 确保pastureId正确传递给筛选组件
3. **数据清理**: 级联选择时注意清理下级选择状态
4. **错误处理**: 接口调用失败时的友好提示
5. **性能优化**: 圈舍和栏位列表使用较大的pageSize减少分页请求
